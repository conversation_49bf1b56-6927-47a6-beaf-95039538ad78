<template>
  <q-card v-if="category != 'HEADER' && matchedItem" class="q-pa-md q-ma-md evaluate-get">
    <div class="row q-ma-md">
      <q-markdown class="title">
        {{ matchedItem?.questions?.[0]?.questionText || 'ไม่พบคำถาม' }}
      </q-markdown>
      <div class="required">*</div>
    </div>
    <div v-if="matchedItem.questions?.[0]?.imagePath" class="row justify-center q-ma-md">
      <q-img
        :src="matchedItem.questions?.[0]?.imagePath"
        fit="scale-down"
        :ratio="1"
        class="centered-image"
        :width="
          matchedItem.questions?.[0]?.imageWidth
            ? matchedItem.questions[0].imageWidth + 'px'
            : undefined
        "
        :height="
          matchedItem.questions?.[0]?.imageHeight
            ? matchedItem.questions[0].imageHeight + 'px'
            : '200px'
        "
      />
    </div>

    <div class="row q-ma-md">
      <div v-if="category === 'RADIO'" class="group font-size">
        <q-radio
          :disable="isPreview"
          v-model="selectedAnswer"
          v-for="choice in matchedItem?.options"
          :key="choice.id ?? choice.optionText"
          :val="String(choice.id)"
          :label="choice.optionText"
          color="primary"
          @update:model-value="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'CHECKBOX'" class="group font-size">
        <q-checkbox
          v-for="choice in matchedItem?.options"
          :key="choice.id"
          :val="String(choice.id)"
          v-model="selectedAnswers"
          :label="choice.optionText"
          color="primary"
          :disable="isPreview"
          @update:model-value="onCheckboxChange"
        />
      </div>

      <div v-else-if="category === 'TEXTFIELD'">
        <q-input
          :disable="isPreview"
          v-model="textAnswer"
          dense
          placeholder="คำตอบ..."
          style="min-width: 400px"
          class="font-size"
          @blur="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'GRID'" class="grid-choice">
        <q-table flat bordered :rows="rows" :columns="columns" row-key="id" hide-bottom>
          <template v-slot:body-cell="props">
            <q-td :props="props">
              <template v-if="props.col.name === 'question'">
                {{ props.row.question }}
              </template>
              <template v-else>
                <q-radio
                  :disable="isPreview"
                  :val="Number(props.col.name.replace('choice_', ''))"
                  v-model="gridAnswers[props.row.id]"
                  size="sm"
                  color="primary"
                  @update:model-value="() => (isEdit = true)"
                />
              </template>
            </q-td>
          </template>
        </q-table>
      </div>
      <div v-else-if="category === 'UPLOAD'">
        <div class="q-mb-md" style="color: #9d9d9d">
          {{
            'อัปโหลด ' +
            matchedItem?.questions?.[0]?.sizeLimit +
            ' ไฟล์ ' +
            matchedItem?.questions?.[0]?.acceptFile +
            ' มากสุด ' +
            matchedItem?.questions?.[0]?.uploadLimit
          }}
        </div>
        <q-file
          :disable="isPreview"
          v-model="fileInputRef"
          label="เพิ่มไฟล์"
          counter
          multiple
          :max-files="matchedItem?.questions?.[0]?.uploadLimit"
          :max-file-size="matchedItem?.questions?.[0]?.sizeLimit"
          dense
          flat
          class="custom-file"
        >
          <template v-slot:prepend>
            <q-icon name="upload" color="amber" />
          </template>
        </q-file>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import type { QTableColumn } from 'quasar';
import { type Assessment, type Option } from 'src/types/models';
import type { Response, ItemBlock } from 'src/types/models';
import { ResponsesService } from 'src/services/asm/responseService';

import { computed, reactive, ref, watch } from 'vue';
import { OptionService } from 'src/services/asm/optionService';

const props = defineProps<{
  id: number;
  draftId: number;
  category: string;
  item: Assessment;
  section: number;
  status: boolean;
  clear: boolean;
}>();

//answer
const selectedAnswer = ref<string>('');
const selectedAnswers = ref<string[]>([]);
const textAnswer = ref('');
const gridAnswers = reactive<{ [key: number]: number }>({});
const lastGridAnswer = ref<{ [key: number]: number }>();
const response = ref<Response>();
const userAnswer = ref<Response[]>();
const isEdit = ref(false);
const fileInputRef = ref<File[] | null>(null);
const isPreview = ref(true);

const matchedItem = ref<ItemBlock | null>(null);

if (props.status === true) {
  isPreview.value = true;
} else {
  isPreview.value = false;
}
type AnswerValue = string | string[] | Record<string, number>;

//checkbox
let previousSelected: string[] = [];
const onCheckboxChange = async (newVal: string[]) => {
  const added = newVal.filter((val) => !previousSelected.includes(val));
  const removed = previousSelected.filter((val) => !newVal.includes(val));

  try {
    const questionId = matchedItem.value?.questions?.[0]?.id;
    if (!questionId) return;
    const res = await new ResponsesService().findAnswer(props.draftId, questionId);
    if (res) {
      response.value = res.data;
    }

    if (response.value) {
      for (const optionId of removed) {
        const selectedOptionId = Number(optionId);
        if (isNaN(selectedOptionId)) continue;

        const removeAnswer = await new ResponsesService().findRemoveCheckBoxAnswer(
          props.draftId,
          questionId,
          selectedOptionId,
        );

        if (removeAnswer) {
          await new ResponsesService().remove(removeAnswer.id);
          console.trace('🗑️ ลบคำตอบ:', removeAnswer.id);
        }
      }
    }

    for (const optionId of added) {
      const selectedOptionId = Number(optionId);
      if (isNaN(selectedOptionId)) continue;

      const payload = {
        id: 0,
        submissionId: props.draftId,
        questionId,
        selectedOptionId,
      };
      await new ResponsesService().create(payload);
    }
  } catch (err) {
    console.log('❌ เพิ่มข้อมูลไม่สำเร็จ', err);
  }

  previousSelected = [...newVal];
};

const emit = defineEmits<{
  (event: 'update-answer', payload: { id: string | number; value: AnswerValue }): void;
}>();
emit('update-answer', {
  id: props.id,
  value:
    props.category === 'CHECKBOX'
      ? selectedAnswers.value
      : props.category === 'GRID'
        ? gridAnswers
        : props.category === 'RADIO'
          ? selectedAnswer.value
          : props.category === 'TEXTFIELD'
            ? textAnswer.value
            : '', // fallback กรณีไม่ตรงกับเงื่อนไขใดเลย
});

const emitAnswer = async () => {
  let value;

  if (props.category === 'GRID') {
    const entries = Object.entries(gridAnswers); // [[key, value], ...]

    const lastEntry = entries.at(-1); // หรือใช้ entries[entries.length - 1]
    if (lastEntry) {
      const [lastKey, lastValue] = lastEntry;

      lastGridAnswer.value = { [parseInt(lastKey, 10)]: lastValue };

      value = lastGridAnswer.value;
    } else {
      lastGridAnswer.value = {};
      value = '';
    }
  } else if (props.category === 'CHECKBOX') {
    value = selectedAnswers.value; // เช่น [1, 2]
  } else if (props.category === 'TEXTFILED') {
    value = textAnswer.value;
  } else if (props.category === 'RADIO') {
    value = selectedAnswer.value;
  } else {
    value = textAnswer.value;
  }

  emit('update-answer', {
    id: props.id,
    value,
  });

  try {
    if (props.category === 'TEXTFIELD') {
      const questionId = matchedItem.value?.questions?.[0]?.id;
      if (questionId) {
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        if (res) {
          response.value = res.data;
        }
        if (response.value?.id && response.value.selectedOptionId) {
          const option = await new OptionService().getOptionById(response.value.selectedOptionId);
          const upDateAnswerText: Option = {
            id: option.id,
            itemBlockId: option.itemBlockId,
            optionText: textAnswer.value,
            value: option.value,
            sequence: option.sequence,
          };
          await new OptionService().updateOption(response.value.selectedOptionId, upDateAnswerText);
        } else {
          const answerText: Option = {
            id: 0,
            itemBlockId: props.id,
            optionText: textAnswer.value,
            value: 0,
            sequence: 0,
          };
          const createdOption = await new OptionService().createOption(answerText);

          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId: questionId,
            selectedOptionId: createdOption.id,
          };
          await new ResponsesService().create(payload);
        }
      }
    } else if (props.category === 'RADIO') {
      const questionId = matchedItem.value?.questions?.[0]?.id;

      if (questionId) {
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        if (res) {
          response.value = res.data;
        }
        if (response.value?.id && response.value.questionId) {
          console.log('update');
          const updatePayload: Response = {
            id: response.value.id,
            submissionId: response.value.submissionId,
            questionId: response.value.questionId,
            selectedOptionId: props.category === 'RADIO' ? parseInt(value as string, 10) : 0,
          };
          console.trace('emitAnswer called', updatePayload);
          await new ResponsesService().update(updatePayload.id, updatePayload);
        } else {
          console.log('create');
          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId: questionId,
            selectedOptionId: props.category === 'RADIO' ? parseInt(value as string, 10) : 0,
          };

          console.trace('emitAnswer called', payload);
          await new ResponsesService().create(payload);
        }
      }
    } else if (props.category === 'GRID' && isEdit.value === true) {
      console.log('in');
      for (const [questionIdStr, selectedOptionId] of Object.entries(gridAnswers)) {
        const questionId = Number(questionIdStr);
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        console.log(res.data);
        if (!res.data || Object.keys(res.data).length === 0) {
          // สร้างใหม่ ถ้า res.data เป็น undefined/null หรือเป็น object ว่าง
          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId,
            selectedOptionId,
          };
          console.trace('emitAnswer create:', payload);
          await new ResponsesService().create(payload);
        } else {
          const updatePayload: Response = {
            id: res.data.id,
            submissionId: props.draftId,
            questionId,
            selectedOptionId,
          };
          console.trace('emitAnswer update:', updatePayload);
          if (updatePayload.id) {
            await new ResponsesService().update(updatePayload.id, updatePayload);
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to send response to backend:', error);
  }
};
watch(
  gridAnswers,
  async () => {
    await emitAnswer();
  },
  { deep: true },
);

watch(
  matchedItem,
  (newItem) => {
    if (newItem) {
      newItem.section = props.section;
    }
  },
  { immediate: true },
);

interface Row {
  id: number;
  question: string;
  [key: `choice_${number}`]: string;
}

const columns = computed<QTableColumn[]>(() => {
  const base: QTableColumn[] = [
    {
      name: 'question',
      label: 'คำถาม',
      field: 'question',
      align: 'left',
    },
  ];

  const choices: QTableColumn[] =
    matchedItem.value?.options?.map((opt) => ({
      name: `choice_${opt.id}`, // ใช้ option.id แทน index
      label: opt.optionText,
      field: `choice_${opt.id}`,
      align: 'center',
    })) ?? [];

  return base.concat(choices);
});

const rows = computed<Row[]>(() => {
  if (!matchedItem.value?.questions) return [];

  return matchedItem.value.questions
    .filter((q) => !q.isHeader) // กรองเอาเฉพาะคำถามที่ไม่ใช่ header
    .map((q) => {
      const row: Row = {
        id: q.id,
        question: q.questionText,
      };

      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });
      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });

      return row;
    });
});

watch(
  () => props.draftId,
  async (newDraftId) => {
    matchedItem.value =
      props.item?.itemBlocks?.find(
        (item) => item.id === props.id && item.section === props.section,
      ) || null;
    console.log(props.draftId);
    const questions = matchedItem.value?.questions ?? [];

    if (questions.length === 0) return;

    if (props.status === true /* isPreview */) {
      // 🟢 preview mode → แค่ map คำถาม ไม่ต้องโหลดคำตอบ
      userAnswer.value = [];
      selectedAnswer.value = '';
      selectedAnswers.value = [];
      textAnswer.value = '';
      return;
    }

    if (!newDraftId) return;

    const isCheckbox = props.category === 'CHECKBOX';
    const answers: Response[] = [];

    for (const question of questions) {
      if (isCheckbox) {
        const res = await new ResponsesService().findAnswers(props.draftId, question.id);
        if (Array.isArray(res)) {
          answers.push(...res);
        }

        selectedAnswers.value = answers
          .map((a) => a.selectedOptionId)
          .filter((id): id is number => typeof id === 'number')
          .map((id) => String(id));
      } else if (props.category === 'GRID') {
        const res = await new ResponsesService().findAnswers(props.draftId, question.id);
        if (Array.isArray(res)) {
          answers.push(...res);
          res.forEach((ans) => {
            if (ans.questionId && ans.selectedOptionId) {
              gridAnswers[ans.questionId] = ans.selectedOptionId;
            }
          });
        }
      } else {
        const answer = await new ResponsesService().findAnswer(newDraftId, question.id);
        if (answer.data) {
          answers.push(answer.data);
        }
      }
    }

    userAnswer.value = answers;

    if (props.category === 'TEXTFIELD' && userAnswer.value) {
      textAnswer.value = userAnswer.value?.[0]?.selectedOption?.optionText ?? '';
    } else if (props.category === 'RADIO' && userAnswer.value) {
      selectedAnswer.value = String(userAnswer.value?.[0]?.selectedOptionId ?? '0');
    }
  },
  { immediate: true },
);

watch(
  () => props.clear,
  (isClear) => {
    if (isClear) {
      console.log('should be clear');
      if (props.category === 'TEXTFIELD') {
        textAnswer.value = '';
      } else if (props.category === 'RADIO') {
        selectedAnswer.value = '';
      } else if (props.category === 'CHECKBOX') {
        selectedAnswers.value = [];
      } else if (props.category === 'GRID') {
        for (const key in gridAnswers) {
          delete gridAnswers[key];
        }
      }
    }
  },
  { immediate: true },
);

// blur → save (เฉพาะ shortAnswer เพราะ radio/checkbox ไม่มี blur)
</script>

<style scoped lang="scss">
.title {
  font-size: 20px;
}

.required {
  color: red;
  font-size: 20px;
  margin-left: 4px;
}
.group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-upload {
  background-color: white;
  color: $primary;
  border: 1px solid;
  border-color: $surface;
}

.font-size {
  font-size: 18px;
}
.grid-choice {
  width: 100%;
  display: flex;
  justify-content: center;
}

.grid-table {
  width: 100%;
  max-width: 800px;
  table-layout: fixed;

  .label-column {
    width: 50%;
    max-width: 50%;
    word-break: break-all;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .option-column {
    width: calc(50% / 5);
    padding: 8px;
    text-align: center;

    &:first-child {
      border-left: none;
    }
  }
}
.custom-file {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 10px 16px;
  font-weight: bold;
  display: inline-block;
  transition: background-color 0.3s;
  max-width: 150px;
  max-height: 60px;
  height: 100%;
  width: 100%;
}

.custom-file:hover {
  background-color: #fffbed;
}
</style>
