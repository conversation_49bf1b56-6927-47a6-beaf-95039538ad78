describe('Quiz Edit Question', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  it('TC-04-02-01 edit question', () => {
    cy.clickEdit();
    cy.get(
      ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > :nth-child(1) > [data-v-f912b43d=""] > .input-wrapper > .editable-div',
    )
      .clear()
      .type('test2');
    cy.get('.q-tab-panel').click();
    cy.get('[href="/quiz/management"] > .q-item__section--side > .q-icon').click();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
    cy.clickEdit();
    cy.get(
      ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > :nth-child(2) > :nth-child(1) > [data-v-f912b43d=""] > .input-wrapper > .editable-div',
    ).should('contain', 'test2');
  });
});
